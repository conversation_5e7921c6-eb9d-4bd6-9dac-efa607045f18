import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Dashboard - Secure File Vault',
  description: 'Manage your encrypted files securely',
};

export default function DashboardPage() {
  return (
    <div className='flex h-screen flex-col'>
      <header className='border-b'>
        <div className='container flex h-16 items-center px-4'>
          <h1 className='text-xl font-semibold'>Secure File Vault</h1>
        </div>
      </header>
      <main className='flex-1 space-y-4 p-8 pt-6'>
        <div className='flex items-center justify-between space-y-2'>
          <h2 className='text-3xl font-bold tracking-tight'>Dashboard</h2>
        </div>
        {/* Dashboard content will be implemented in later tasks */}
        <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
          <div className='rounded-lg border p-4'>
            <h3 className='font-semibold'>Files</h3>
            <p className='text-sm text-muted-foreground'>
              File management coming soon...
            </p>
          </div>
          <div className='rounded-lg border p-4'>
            <h3 className='font-semibold'>Storage</h3>
            <p className='text-sm text-muted-foreground'>
              Storage metrics coming soon...
            </p>
          </div>
          <div className='rounded-lg border p-4'>
            <h3 className='font-semibold'>Shares</h3>
            <p className='text-sm text-muted-foreground'>
              Sharing management coming soon...
            </p>
          </div>
          <div className='rounded-lg border p-4'>
            <h3 className='font-semibold'>Security</h3>
            <p className='text-sm text-muted-foreground'>
              Security settings coming soon...
            </p>
          </div>
        </div>
      </main>
    </div>
  );
}
