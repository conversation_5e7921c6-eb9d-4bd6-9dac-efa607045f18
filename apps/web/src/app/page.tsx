import { <PERSON>, Lock, Eye, Users } from 'lucide-react';
import Link from 'next/link';

export default function Home() {
  return (
    <div className='flex min-h-screen flex-col'>
      {/* Header */}
      <header className='border-b'>
        <div className='container flex h-16 items-center justify-between px-4'>
          <div className='flex items-center space-x-2'>
            <Shield className='h-6 w-6 text-primary' />
            <span className='text-xl font-bold'>Secure File Vault</span>
          </div>
          <nav className='flex items-center space-x-4'>
            <Link
              href='/auth/login'
              className='text-sm font-medium text-muted-foreground transition-colors hover:text-primary'
            >
              Login
            </Link>
            <Link
              href='/auth/register'
              className='rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground transition-colors hover:bg-primary/90'
            >
              Get Started
            </Link>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <main className='flex-1'>
        <section className='container space-y-6 pb-8 pt-6 md:pb-12 md:pt-10 lg:py-32'>
          <div className='mx-auto flex max-w-[58rem] flex-col items-center space-y-4 text-center'>
            <h1 className='font-bold text-3xl leading-[1.1] sm:text-3xl md:text-6xl'>
              Your data, your eyes only
            </h1>
            <p className='max-w-[85%] leading-normal text-muted-foreground sm:text-lg sm:leading-7'>
              Zero-knowledge, end-to-end encrypted file storage platform
              designed for privacy-conscious individuals and organizations
              handling sensitive information.
            </p>
            <div className='space-x-4'>
              <Link
                href='/auth/register'
                className='inline-flex h-11 items-center justify-center rounded-md bg-primary px-8 text-sm font-medium text-primary-foreground ring-offset-background transition-colors hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2'
              >
                Start Free Trial
              </Link>
              <Link
                href='/auth/login'
                className='inline-flex h-11 items-center justify-center rounded-md border border-input bg-background px-8 text-sm font-medium ring-offset-background transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2'
              >
                Sign In
              </Link>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className='container space-y-6 bg-slate-50 py-8 dark:bg-transparent md:py-12 lg:py-24'>
          <div className='mx-auto flex max-w-[58rem] flex-col items-center space-y-4 text-center'>
            <h2 className='font-bold text-3xl leading-[1.1] sm:text-3xl md:text-6xl'>
              Security First
            </h2>
            <p className='max-w-[85%] leading-normal text-muted-foreground sm:text-lg sm:leading-7'>
              Built with zero-knowledge architecture where even we cannot access
              your data.
            </p>
          </div>
          <div className='mx-auto grid justify-center gap-4 sm:grid-cols-2 md:max-w-[64rem] md:grid-cols-3'>
            <div className='relative overflow-hidden rounded-lg border bg-background p-2'>
              <div className='flex h-[180px] flex-col justify-between rounded-md p-6'>
                <Lock className='h-12 w-12 text-primary' />
                <div className='space-y-2'>
                  <h3 className='font-bold'>End-to-End Encryption</h3>
                  <p className='text-sm text-muted-foreground'>
                    AES-256-GCM encryption with client-side key generation
                  </p>
                </div>
              </div>
            </div>
            <div className='relative overflow-hidden rounded-lg border bg-background p-2'>
              <div className='flex h-[180px] flex-col justify-between rounded-md p-6'>
                <Eye className='h-12 w-12 text-primary' />
                <div className='space-y-2'>
                  <h3 className='font-bold'>Zero-Knowledge</h3>
                  <p className='text-sm text-muted-foreground'>
                    We never see your data or encryption keys
                  </p>
                </div>
              </div>
            </div>
            <div className='relative overflow-hidden rounded-lg border bg-background p-2'>
              <div className='flex h-[180px] flex-col justify-between rounded-md p-6'>
                <Users className='h-12 w-12 text-primary' />
                <div className='space-y-2'>
                  <h3 className='font-bold'>Secure Sharing</h3>
                  <p className='text-sm text-muted-foreground'>
                    Share files securely with granular access controls
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className='border-t py-6 md:py-0'>
        <div className='container flex flex-col items-center justify-between gap-4 md:h-24 md:flex-row'>
          <div className='flex flex-col items-center gap-4 px-8 md:flex-row md:gap-2 md:px-0'>
            <Shield className='h-6 w-6' />
            <p className='text-center text-sm leading-loose text-muted-foreground md:text-left'>
              Built for privacy-conscious individuals and organizations.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
