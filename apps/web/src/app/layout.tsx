import type { Metadata } from 'next';
import { Inter, JetBrains_Mono } from 'next/font/google';

import './globals.css';
import { ThemeProvider } from '@/components/theme-provider';

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
});

const jetbrainsMono = JetBrains_Mono({
  subsets: ['latin'],
  variable: '--font-mono',
  display: 'swap',
});

export const metadata: Metadata = {
  title: 'Secure File Vault',
  description:
    'Zero-knowledge, end-to-end encrypted file storage platform. Your data, your eyes only.',
  keywords: [
    'encryption',
    'file storage',
    'zero-knowledge',
    'privacy',
    'security',
  ],
  authors: [{ name: 'Secure File Vault Team' }],
  creator: 'Secure File Vault',
  publisher: 'Secure File Vault',
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://securefilevault.com',
    title: 'Secure File Vault',
    description: 'Zero-knowledge, end-to-end encrypted file storage platform',
    siteName: 'Secure File Vault',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Secure File Vault',
    description: 'Zero-knowledge, end-to-end encrypted file storage platform',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang='en' suppressHydrationWarning>
      <body
        className={`${inter.variable} ${jetbrainsMono.variable} font-sans antialiased`}
      >
        <ThemeProvider
          attribute='class'
          defaultTheme='system'
          enableSystem
          disableTransitionOnChange
        >
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}
