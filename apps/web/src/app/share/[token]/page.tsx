import { Metadata } from 'next';

interface SharePageProps {
  params: {
    token: string;
  };
}

export const metadata: Metadata = {
  title: 'Shared File - Secure File Vault',
  description: 'Access a securely shared file',
};

export default function SharePage({ params }: SharePageProps) {
  return (
    <div className='container flex h-screen w-screen flex-col items-center justify-center'>
      <div className='mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[450px]'>
        <div className='flex flex-col space-y-2 text-center'>
          <h1 className='text-2xl font-semibold tracking-tight'>
            Shared File Access
          </h1>
          <p className='text-sm text-muted-foreground'>
            Accessing shared file with token: {params.token}
          </p>
        </div>
        {/* Shared file access will be implemented in later tasks */}
        <div className='grid gap-6'>
          <div className='text-center text-sm text-muted-foreground'>
            Shared file access coming soon...
          </div>
        </div>
      </div>
    </div>
  );
}
