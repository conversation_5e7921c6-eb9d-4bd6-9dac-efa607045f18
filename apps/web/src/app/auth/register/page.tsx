import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Register - Secure File Vault',
  description: 'Create your secure, zero-knowledge file vault account',
};

export default function RegisterPage() {
  return (
    <div className='container flex h-screen w-screen flex-col items-center justify-center'>
      <div className='mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]'>
        <div className='flex flex-col space-y-2 text-center'>
          <h1 className='text-2xl font-semibold tracking-tight'>
            Create an account
          </h1>
          <p className='text-sm text-muted-foreground'>
            Start your journey with zero-knowledge file storage
          </p>
        </div>
        {/* Registration form will be implemented in later tasks */}
        <div className='grid gap-6'>
          <div className='text-center text-sm text-muted-foreground'>
            Registration form coming soon...
          </div>
        </div>
      </div>
    </div>
  );
}
