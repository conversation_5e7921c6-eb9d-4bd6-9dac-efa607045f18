import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Login - Secure File Vault',
  description: 'Securely log in to your encrypted file vault',
};

export default function LoginPage() {
  return (
    <div className='container flex h-screen w-screen flex-col items-center justify-center'>
      <div className='mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]'>
        <div className='flex flex-col space-y-2 text-center'>
          <h1 className='text-2xl font-semibold tracking-tight'>
            Welcome back
          </h1>
          <p className='text-sm text-muted-foreground'>
            Enter your credentials to access your secure vault
          </p>
        </div>
        {/* Login form will be implemented in later tasks */}
        <div className='grid gap-6'>
          <div className='text-center text-sm text-muted-foreground'>
            Login form coming soon...
          </div>
        </div>
      </div>
    </div>
  );
}
