import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, Type, ValidationPipe } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

// Global test setup for E2E tests
let app: INestApplication | undefined;

// Setup function to create test application
export const createTestApp = async (
  moduleClass: Type<unknown>
): Promise<INestApplication> => {
  const moduleFixture: TestingModule = await Test.createTestingModule({
    imports: [
      ConfigModule.forRoot({
        isGlobal: true,
        envFilePath: '.env.test',
      }),
      moduleClass,
    ],
  }).compile();

  app = moduleFixture.createNestApplication();

  // Apply the same configuration as the main app
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    })
  );

  app.setGlobalPrefix('api');

  await app.init();
  return app;
};

// Cleanup function
export const closeTestApp = async (): Promise<void> => {
  if (app) {
    await app.close();
  }
};

// Global setup and teardown
beforeAll(async () => {
  // Set test environment
  process.env.NODE_ENV = 'test';

  // Set default test database URL if not provided
  process.env.DATABASE_URL ??=
    'postgresql://ci_test_user:ci_test_pass_temp@localhost:5432/secure_vault_dev';

  // Set default Redis URL if not provided
  process.env.REDIS_URL ??= 'redis://:ci_redis_pass_temp@localhost:6379';
});

afterAll(async () => {
  await closeTestApp();
});

// Increase timeout for E2E tests
jest.setTimeout(30000);
