# ==============================================
# API APPLICATION - ENVIRONMENT VARIABLES
# ==============================================

# Database
DATABASE_URL="postgresql://vault_user:vault_password@localhost:5432/secure_vault_dev?schema=public"

# Redis
REDIS_URL="redis://:redis_password@localhost:6380"

# Server Configuration
PORT=3001
NODE_ENV="development"

# JWT Configuration
JWT_SECRET="your-super-secure-jwt-secret-key-change-in-production"
JWT_EXPIRES_IN="7d"

# CORS
FRONTEND_URL="http://localhost:3000"

# File Storage
STORAGE_PROVIDER="local"
STORAGE_PATH="./uploads"
MAX_FILE_SIZE="**********"

# Security
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL="debug"
ENABLE_REQUEST_LOGGING=true