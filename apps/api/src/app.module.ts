import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ThrottlerModule } from '@nestjs/throttler';

import { AuthModule } from './auth/auth.module';
import { DatabaseModule } from './database/database.module';
import { FileModule } from './file/file.module';
import { ShareModule } from './share/share.module';
import { UserModule } from './user/user.module';

@Module({
  imports: [
    // Environment configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),

    // Rate limiting
    ThrottlerModule.forRoot([
      {
        ttl: 60000, // 1 minute
        limit: 100, // 100 requests per minute
      },
    ]),

    // Database module
    DatabaseModule,

    // Feature modules
    AuthModule,
    UserModule,
    FileModule,
    ShareModule,
  ],
})
export class AppModule {}
