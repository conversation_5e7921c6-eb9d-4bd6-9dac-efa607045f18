import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional } from 'class-validator';

export class UploadFileDto {
  @ApiProperty({ description: 'Encrypted filename' })
  @IsString()
  encryptedName: string;

  @ApiProperty({ description: 'Encrypted file size' })
  @IsString()
  encryptedSize: string;

  @ApiProperty({ description: 'Encrypted MIME type' })
  @IsString()
  encryptedMimeType: string;

  @ApiProperty({
    description: 'File encryption key, encrypted with user master key',
  })
  @IsString()
  encryptedFileKey: string;

  @ApiProperty({ description: 'Cloud storage location identifier' })
  @IsString()
  storageLocation: string;

  @ApiProperty({ description: 'SHA-256 checksum for integrity verification' })
  @IsString()
  checksum: string;

  @ApiProperty({ description: 'Folder ID for organization', required: false })
  @IsOptional()
  @IsString()
  folderId?: string;

  @ApiProperty({
    description: 'Encrypted tags as JSON string',
    required: false,
  })
  @IsOptional()
  @IsString()
  encryptedTags?: string;
}
