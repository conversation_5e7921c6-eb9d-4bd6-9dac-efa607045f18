import {
  Injectable,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';

import { DatabaseService } from '../database/database.service';

import { UploadFileDto } from './dto/upload-file.dto';

@Injectable()
export class FileService {
  constructor(private readonly db: DatabaseService) {}

  async uploadFile(userId: string, uploadFileDto: UploadFileDto) {
    // Check user storage quota
    const user = await this.db.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const fileSize = BigInt(uploadFileDto.encryptedSize);
    if (user.storageUsed + fileSize > user.storageQuota) {
      throw new ForbiddenException('Storage quota exceeded');
    }

    // Create file record
    const file = await this.db.fileRecord.create({
      data: {
        userId,
        encryptedName: uploadFileDto.encryptedName,
        encryptedSize: uploadFileDto.encryptedSize,
        encryptedMimeType: uploadFileDto.encryptedMimeType,
        encryptedFileKey: uploadFileDto.encryptedFileKey,
        storageLocation: uploadFileDto.storageLocation,
        checksum: uploadFileDto.checksum,
        folderId: uploadFileDto.folderId,
        encryptedTags: uploadFileDto.encryptedTags,
      },
    });

    // Update user storage usage
    await this.db.user.update({
      where: { id: userId },
      data: { storageUsed: user.storageUsed + fileSize },
    });

    return file;
  }

  async getFiles(userId: string, folderId?: string) {
    return this.db.fileRecord.findMany({
      where: {
        userId,
        folderId: folderId || null,
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  async getFile(userId: string, fileId: string) {
    const file = await this.db.fileRecord.findFirst({
      where: {
        id: fileId,
        userId,
      },
    });

    if (!file) {
      throw new NotFoundException('File not found');
    }

    return file;
  }

  async deleteFile(userId: string, fileId: string) {
    const file = await this.getFile(userId, fileId);

    // Delete file record
    await this.db.fileRecord.delete({
      where: { id: fileId },
    });

    // Update user storage usage
    const user = await this.db.user.findUnique({
      where: { id: userId },
    });

    if (user) {
      const fileSize = BigInt(file.encryptedSize);
      await this.db.user.update({
        where: { id: userId },
        data: { storageUsed: user.storageUsed - fileSize },
      });
    }

    return { message: 'File deleted successfully' };
  }
}
