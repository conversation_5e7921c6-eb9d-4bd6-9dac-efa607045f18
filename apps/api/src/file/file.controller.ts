import {
  Controller,
  Get,
  Post,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';

import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

import { UploadFileDto } from './dto/upload-file.dto';
import { FileService } from './file.service';

@ApiTags('Files')
@Controller('files')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class FileController {
  constructor(private readonly fileService: FileService) {}

  @Post('upload')
  @ApiOperation({ summary: 'Upload an encrypted file' })
  @ApiResponse({ status: 201, description: 'File uploaded successfully' })
  @ApiResponse({ status: 403, description: 'Storage quota exceeded' })
  async uploadFile(
    @CurrentUser() user: any,
    @Body() uploadFileDto: UploadFileDto
  ) {
    return this.fileService.uploadFile(user.id, uploadFileDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get user files' })
  @ApiResponse({ status: 200, description: 'Files retrieved successfully' })
  async getFiles(
    @CurrentUser() user: any,
    @Query('folderId') folderId?: string
  ) {
    return this.fileService.getFiles(user.id, folderId);
  }

  @Get(':fileId')
  @ApiOperation({ summary: 'Get a specific file' })
  @ApiResponse({ status: 200, description: 'File retrieved successfully' })
  @ApiResponse({ status: 404, description: 'File not found' })
  async getFile(@CurrentUser() user: any, @Param('fileId') fileId: string) {
    return this.fileService.getFile(user.id, fileId);
  }

  @Delete(':fileId')
  @ApiOperation({ summary: 'Delete a file' })
  @ApiResponse({ status: 200, description: 'File deleted successfully' })
  @ApiResponse({ status: 404, description: 'File not found' })
  async deleteFile(@CurrentUser() user: any, @Param('fileId') fileId: string) {
    return this.fileService.deleteFile(user.id, fileId);
  }
}
