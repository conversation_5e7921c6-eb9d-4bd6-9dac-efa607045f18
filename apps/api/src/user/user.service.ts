import {
  Injectable,
  ConflictException,
  NotFoundException,
} from '@nestjs/common';

import { DatabaseService } from '../database/database.service';

import { CreateUserDto } from './dto/create-user.dto';

@Injectable()
export class UserService {
  constructor(private readonly db: DatabaseService) {}

  async create(createUserDto: CreateUserDto) {
    // Check if user already exists
    const existingUser = await this.db.user.findUnique({
      where: { email: createUserDto.email },
    });

    if (existingUser) {
      throw new ConflictException('User already exists');
    }

    // Create new user
    return this.db.user.create({
      data: {
        email: createUserDto.email,
        encryptedMasterKey: createUserDto.encryptedMasterKey,
        keyDerivationSalt: createUserDto.keyDerivationSalt,
        keyDerivationIterations: createUserDto.keyDerivationIterations,
        publicKey: createUserDto.publicKey,
      },
    });
  }

  async findById(id: string) {
    const user = await this.db.user.findUnique({
      where: { id },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  async findByEmail(email: string) {
    return this.db.user.findUnique({
      where: { email },
    });
  }

  async validateUser(email: string, _challengeResponse: string) {
    // In a zero-knowledge system, this would involve cryptographic verification
    // For now, we'll implement a basic structure that can be enhanced later
    const user = await this.findByEmail(email);

    if (!user) {
      return null;
    }

    // TODO: Implement zero-knowledge challenge-response validation
    // This is a placeholder that should be replaced with proper crypto validation
    return user;
  }

  async updateLastLogin(userId: string) {
    return this.db.user.update({
      where: { id: userId },
      data: { lastLoginAt: new Date() },
    });
  }

  async updateStorageUsed(userId: string, storageUsed: bigint) {
    return this.db.user.update({
      where: { id: userId },
      data: { storageUsed },
    });
  }
}
