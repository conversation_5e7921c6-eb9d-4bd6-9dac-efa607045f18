import { randomBytes } from 'crypto';

import {
  Injectable,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';

import { DatabaseService } from '../database/database.service';
import { FileService } from '../file/file.service';

import { CreateShareDto } from './dto/create-share.dto';

@Injectable()
export class ShareService {
  constructor(
    private readonly db: DatabaseService,
    private readonly fileService: FileService
  ) {}

  async createShare(userId: string, createShareDto: CreateShareDto) {
    // Verify user owns the file
    const file = await this.fileService.getFile(userId, createShareDto.fileId);

    if (!file) {
      throw new NotFoundException('File not found');
    }

    // Generate unique share token
    const shareToken = randomBytes(32).toString('hex');

    // Create share record
    const share = await this.db.shareRecord.create({
      data: {
        fileId: createShareDto.fileId,
        ownerId: userId,
        shareToken,
        encryptedFileKey: createShareDto.encryptedFileKey,
        canDownload: createShareDto.canDownload ?? true,
        canView: createShareDto.canView ?? true,
        expiresAt: createShareDto.expiresAt,
        maxAccesses: createShareDto.maxAccesses,
        passwordProtected: createShareDto.passwordProtected ?? false,
        encryptedPassword: createShareDto.encryptedPassword,
      },
    });

    return {
      id: share.id,
      shareToken: share.shareToken,
      canDownload: share.canDownload,
      canView: share.canView,
      expiresAt: share.expiresAt,
      maxAccesses: share.maxAccesses,
      passwordProtected: share.passwordProtected,
      createdAt: share.createdAt,
    };
  }

  async getShares(userId: string) {
    return this.db.shareRecord.findMany({
      where: { ownerId: userId },
      include: {
        file: {
          select: {
            id: true,
            encryptedName: true,
            createdAt: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  async getShareByToken(shareToken: string) {
    const share = await this.db.shareRecord.findUnique({
      where: { shareToken },
      include: {
        file: true,
      },
    });

    if (!share) {
      throw new NotFoundException('Share not found');
    }

    // Check if share has expired
    if (share.expiresAt && share.expiresAt < new Date()) {
      throw new ForbiddenException('Share has expired');
    }

    // Check if max accesses reached
    if (share.maxAccesses && share.currentAccesses >= share.maxAccesses) {
      throw new ForbiddenException('Share access limit reached');
    }

    return share;
  }

  async accessShare(shareToken: string, password?: string) {
    const share = await this.getShareByToken(shareToken);

    // Check password if required
    if (share.passwordProtected && !password) {
      throw new ForbiddenException('Password required');
    }

    // TODO: Implement password verification for protected shares

    // Log access
    await this.db.shareAccessLog.create({
      data: {
        shareId: share.id,
        action: 'VIEW',
        // Note: In production, IP and user agent should be hashed for privacy
      },
    });

    // Increment access count
    await this.db.shareRecord.update({
      where: { id: share.id },
      data: { currentAccesses: share.currentAccesses + 1 },
    });

    return {
      file: {
        id: share.file.id,
        encryptedName: share.file.encryptedName,
        encryptedSize: share.file.encryptedSize,
        encryptedMimeType: share.file.encryptedMimeType,
        storageLocation: share.file.storageLocation,
      },
      encryptedFileKey: share.encryptedFileKey,
      permissions: {
        canView: share.canView,
        canDownload: share.canDownload,
      },
    };
  }

  async revokeShare(userId: string, shareId: string) {
    const share = await this.db.shareRecord.findFirst({
      where: {
        id: shareId,
        ownerId: userId,
      },
    });

    if (!share) {
      throw new NotFoundException('Share not found');
    }

    await this.db.shareRecord.delete({
      where: { id: shareId },
    });

    return { message: 'Share revoked successfully' };
  }
}
