import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsBoolean,
  IsOptional,
  IsDateString,
  IsInt,
  Min,
} from 'class-validator';

export class CreateShareDto {
  @ApiProperty({ description: 'File ID to share' })
  @IsString()
  fileId: string;

  @ApiProperty({ description: 'File encryption key encrypted for sharing' })
  @IsString()
  encryptedFileKey: string;

  @ApiProperty({
    description: 'Allow download permission',
    required: false,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  canDownload?: boolean;

  @ApiProperty({
    description: 'Allow view permission',
    required: false,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  canView?: boolean;

  @ApiProperty({ description: 'Share expiration date', required: false })
  @IsOptional()
  @IsDateString()
  expiresAt?: Date;

  @ApiProperty({ description: 'Maximum number of accesses', required: false })
  @IsOptional()
  @IsInt()
  @Min(1)
  maxAccesses?: number;

  @ApiProperty({
    description: 'Whether share is password protected',
    required: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  passwordProtected?: boolean;

  @ApiProperty({
    description: 'Encrypted password for protected shares',
    required: false,
  })
  @IsOptional()
  @IsString()
  encryptedPassword?: string;
}
