import {
  Controller,
  Get,
  Post,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';

import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

import { CreateShareDto } from './dto/create-share.dto';
import { ShareService } from './share.service';

@ApiTags('Shares')
@Controller('shares')
export class ShareController {
  constructor(private readonly shareService: ShareService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new share link' })
  @ApiResponse({ status: 201, description: 'Share created successfully' })
  @ApiResponse({ status: 404, description: 'File not found' })
  async createShare(
    @CurrentUser() user: any,
    @Body() createShareDto: CreateShareDto
  ) {
    return this.shareService.createShare(user.id, createShareDto);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user shares' })
  @ApiResponse({ status: 200, description: 'Shares retrieved successfully' })
  async getShares(@CurrentUser() user: any) {
    return this.shareService.getShares(user.id);
  }

  @Get(':shareToken')
  @ApiOperation({ summary: 'Access a shared file' })
  @ApiResponse({
    status: 200,
    description: 'Shared file accessed successfully',
  })
  @ApiResponse({ status: 404, description: 'Share not found' })
  @ApiResponse({ status: 403, description: 'Share expired or access denied' })
  async accessShare(
    @Param('shareToken') shareToken: string,
    @Query('password') password?: string
  ) {
    return this.shareService.accessShare(shareToken, password);
  }

  @Delete(':shareId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Revoke a share' })
  @ApiResponse({ status: 200, description: 'Share revoked successfully' })
  @ApiResponse({ status: 404, description: 'Share not found' })
  async revokeShare(
    @CurrentUser() user: any,
    @Param('shareId') shareId: string
  ) {
    return this.shareService.revokeShare(user.id, shareId);
  }
}
