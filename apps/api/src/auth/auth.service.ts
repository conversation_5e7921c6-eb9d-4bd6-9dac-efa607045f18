import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';

import { UserService } from '../user/user.service';

import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';

@Injectable()
export class AuthService {
  constructor(
    private readonly userService: UserService,
    private readonly jwtService: JwtService
  ) {}

  async register(registerDto: RegisterDto) {
    // Create user with encrypted master key
    const user = await this.userService.create({
      email: registerDto.email,
      encryptedMasterKey: registerDto.encryptedMasterKey,
      keyDerivationSalt: registerDto.keyDerivationSalt,
      keyDerivationIterations: registerDto.keyDerivationIterations,
      publicKey: registerDto.publicKey,
    });

    // Generate JWT token
    const payload = { sub: user.id, email: user.email };
    const accessToken = this.jwtService.sign(payload);

    return {
      user: {
        id: user.id,
        email: user.email,
        tier: user.tier,
        storageQuota: user.storageQuota,
        storageUsed: user.storageUsed,
      },
      accessToken,
    };
  }

  async login(loginDto: LoginDto) {
    // Validate user credentials (zero-knowledge approach)
    const user = await this.userService.validateUser(
      loginDto.email,
      loginDto.challengeResponse
    );

    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Update last login
    await this.userService.updateLastLogin(user.id);

    // Generate JWT token
    const payload = { sub: user.id, email: user.email };
    const accessToken = this.jwtService.sign(payload);

    return {
      user: {
        id: user.id,
        email: user.email,
        tier: user.tier,
        storageQuota: user.storageQuota,
        storageUsed: user.storageUsed,
      },
      accessToken,
    };
  }

  async validateUser(userId: string) {
    return this.userService.findById(userId);
  }
}
