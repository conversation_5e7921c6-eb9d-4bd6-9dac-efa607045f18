import { ApiProperty } from '@nestjs/swagger';
import { IsE<PERSON>, IsString, IsI<PERSON>, Min, <PERSON> } from 'class-validator';

export class RegisterDto {
  @ApiProperty({ description: 'User email address' })
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'Encrypted master key (encrypted with password-derived key)',
  })
  @IsString()
  encryptedMasterKey: string;

  @ApiProperty({ description: 'Salt for key derivation' })
  @IsString()
  keyDerivationSalt: string;

  @ApiProperty({
    description: 'Number of PBKDF2 iterations',
    minimum: 100000,
    maximum: 1000000,
  })
  @IsInt()
  @Min(100000)
  @Max(1000000)
  keyDerivationIterations: number;

  @ApiProperty({ description: 'RSA public key for sharing operations' })
  @IsString()
  publicKey: string;
}
