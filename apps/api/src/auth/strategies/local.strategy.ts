import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-local';

import { UserService } from '../../user/user.service';

@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy) {
  constructor(private readonly userService: UserService) {
    super({
      usernameField: 'email',
      passwordField: 'challengeResponse',
    });
  }

  async validate(email: string, challengeResponse: string): Promise<any> {
    const user = await this.userService.validateUser(email, challengeResponse);
    if (!user) {
      throw new UnauthorizedException();
    }
    return user;
  }
}
