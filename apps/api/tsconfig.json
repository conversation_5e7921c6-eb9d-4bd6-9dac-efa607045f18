{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2021", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "moduleResolution": "node", "esModuleInterop": true, "resolveJsonModule": true, "paths": {"@shared-types/index": ["../../packages/shared-types/src"], "@secure-vault/database": ["../../libs/database/src"], "@secure-vault/auth": ["../../libs/auth/src"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}