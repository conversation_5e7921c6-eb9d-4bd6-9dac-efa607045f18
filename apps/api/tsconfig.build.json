{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "./dist", "baseUrl": "./", "paths": {"@shared-types/*": ["../../packages/shared-types/src/*"], "@crypto-core/*": ["../../packages/crypto-core/src/*"], "@ui-components/*": ["../../packages/ui-components/src/*"], "@api-client/*": ["../../packages/api-client/src/*"], "@config/*": ["../../packages/config/src/*"], "@database/*": ["../../libs/database/src/*"], "@auth/*": ["../../libs/auth/src/*"], "@testing/*": ["../../libs/testing/src/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "test", "**/*spec.ts", "**/*test.ts"]}