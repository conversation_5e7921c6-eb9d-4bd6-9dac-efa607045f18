// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                    String   @id @default(cuid())
  email                 String   @unique
  encryptedMasterKey    String   // Encrypted with password-derived key
  keyDerivationSalt     String   // Salt for PBKDF2
  keyDerivationIterations Int    @default(100000)
  publicKey             String   // RSA public key for sharing
  storageQuota          BigInt   @default(**********) // 5GB in bytes
  storageUsed           BigInt   @default(0)
  tier                  UserTier @default(FREE)
  twoFactorEnabled      Boolean  @default(false)
  twoFactorSecret       String?  // Encrypted 2FA secret
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
  lastLoginAt           DateTime?

  // Relations
  files                 FileRecord[]
  ownedShares           ShareRecord[] @relation("ShareOwner")

  @@map("users")
}

model FileRecord {
  id                    String   @id @default(cuid())
  userId                String
  encryptedName         String   // Encrypted filename
  encryptedSize         String   // Encrypted file size
  encryptedMimeType     String   // Encrypted MIME type
  encryptedFileKey      String   // File encryption key, encrypted with user's master key
  storageLocation       String   // Cloud storage path/identifier
  checksum              String   // SHA-256 checksum for integrity
  folderId              String?  // For future folder organization
  encryptedTags         String?  // JSON array of encrypted tags
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  // Relations
  user                  User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  shares                ShareRecord[]

  @@map("files")
}

model ShareRecord {
  id                    String   @id @default(cuid())
  fileId                String
  ownerId               String
  shareToken            String   @unique // Unique access token
  encryptedFileKey      String   // File key encrypted for sharing
  canDownload           Boolean  @default(true)
  canView               Boolean  @default(true)
  expiresAt             DateTime?
  maxAccesses           Int?
  currentAccesses       Int      @default(0)
  passwordProtected     Boolean  @default(false)
  encryptedPassword     String?  // Hashed password for protected shares
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  // Relations
  file                  FileRecord @relation(fields: [fileId], references: [id], onDelete: Cascade)
  owner                 User       @relation("ShareOwner", fields: [ownerId], references: [id], onDelete: Cascade)
  accessLogs            ShareAccessLog[]

  @@map("shares")
}

model ShareAccessLog {
  id                    String   @id @default(cuid())
  shareId               String
  ipAddress             String?  // Hashed for privacy
  userAgent             String?  // Hashed for privacy
  accessedAt            DateTime @default(now())
  action                ShareAction

  // Relations
  share                 ShareRecord @relation(fields: [shareId], references: [id], onDelete: Cascade)

  @@map("share_access_logs")
}

enum UserTier {
  FREE
  PROFESSIONAL
  BUSINESS
}

enum ShareAction {
  VIEW
  DOWNLOAD
  ACCESS_DENIED
}