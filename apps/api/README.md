# Secure File Vault API

NestJS backend API for the Secure File Vault zero-knowledge encrypted file storage platform.

## Features

- **Zero-Knowledge Architecture**: Server never has access to user data or encryption keys
- **JWT Authentication**: Secure token-based authentication
- **File Management**: Upload, download, and manage encrypted files
- **Secure Sharing**: Create and manage secure file sharing links
- **Rate Limiting**: Built-in rate limiting for API protection
- **Swagger Documentation**: Auto-generated API documentation

## Tech Stack

- **NestJS**: Node.js framework with TypeScript
- **Prisma**: Modern database ORM with PostgreSQL
- **JWT**: JSON Web Tokens for authentication
- **Swagger**: API documentation
- **Helmet**: Security middleware

## Project Structure

```
src/
├── auth/           # Authentication module
├── user/           # User management module
├── file/           # File operations module
├── share/          # File sharing module
├── database/       # Database service and configuration
├── app.module.ts   # Main application module
└── main.ts         # Application entry point
```

## Environment Variables

Copy the `.env.example` file to `.env` and update the values for your environment:

```bash
cp .env.example .env
```

The required environment variables are:

```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/secure_file_vault?schema=public"

# JWT Configuration
JWT_SECRET="your-secret-key"
JWT_EXPIRES_IN="24h"

# Server Configuration
PORT=3001
NODE_ENV="development"

# Frontend URL for CORS
FRONTEND_URL="http://localhost:3000"
```

## Getting Started

1. **Install dependencies** (from project root):

   ```bash
   npm install
   ```

2. **Set up the database**:

   ```bash
   # Generate Prisma client
   npx prisma generate

   # Run database migrations
   npx prisma db push
   ```

3. **Start the development server**:

   ```bash
   npm run dev
   ```

4. **Access the API**:
   - API: http://localhost:3001/api
   - Documentation: http://localhost:3001/api/docs

## Available Scripts

- `npm run build` - Build the application
- `npm run start` - Start the production server
- `npm run start:dev` - Start the development server with hot reload
- `npm run lint` - Run ESLint
- `npm run test` - Run tests
- `npm run type-check` - Run TypeScript type checking

## API Endpoints

### Authentication

- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login user

### Users

- `GET /api/users/profile` - Get current user profile

### Files

- `POST /api/files/upload` - Upload an encrypted file
- `GET /api/files` - Get user files
- `GET /api/files/:fileId` - Get specific file
- `DELETE /api/files/:fileId` - Delete a file

### Shares

- `POST /api/shares` - Create a share link
- `GET /api/shares` - Get user shares
- `GET /api/shares/:shareToken` - Access shared file
- `DELETE /api/shares/:shareId` - Revoke a share

## Database Schema

The application uses PostgreSQL with Prisma ORM. Key models:

- **User**: User accounts with encrypted master keys
- **FileRecord**: Encrypted file metadata and storage references
- **ShareRecord**: Secure file sharing configurations
- **ShareAccessLog**: Audit trail for share access

## Security Features

- **Zero-Knowledge**: All encryption/decryption happens client-side
- **Rate Limiting**: 100 requests per minute per IP
- **CORS Protection**: Configured for frontend domain
- **Helmet Security**: Security headers and protections
- **Input Validation**: Request validation with class-validator
- **JWT Authentication**: Secure token-based auth

## Development

The API is part of a monorepo structure and shares packages with:

- `@shared-types/index`: Common TypeScript types
- `@secure-vault/database`: Database utilities
- `@secure-vault/auth`: Authentication utilities

## Production Deployment

1. Set production environment variables
2. Run database migrations: `npx prisma migrate deploy`
3. Build the application: `npm run build`
4. Start the server: `npm run start:prod`

## Contributing

This API follows NestJS best practices and conventions. All modules should:

- Use dependency injection
- Include proper error handling
- Have comprehensive input validation
- Include Swagger documentation
- Follow the zero-knowledge architecture principles
