{"name": "@secure-file-vault/api", "version": "0.1.0", "description": "NestJS backend API for Secure File Vault", "private": true, "scripts": {"build": "nest build && npm run postbuild", "postbuild": "cp -r dist/apps/api/src/* dist/", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "npm run build && node dist/main.js", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main.js", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest --passWithNoTests", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "npm run build && node dist/main.js"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/config": "^3.1.1", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.2", "@nestjs/swagger": "^7.1.17", "@nestjs/throttler": "^5.0.1", "@prisma/client": "^5.7.0", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "helmet": "^7.1.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "@shared-types/index": "file:../../packages/shared-types", "@secure-vault/database": "file:../../libs/database", "@secure-vault/auth": "file:../../libs/auth"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcrypt": "^5.0.2", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/passport-jwt": "^3.0.13", "@types/passport-local": "^1.0.38", "@types/supertest": "^2.0.12", "jest": "^29.5.0", "prisma": "^5.7.0", "rimraf": "^5.0.5", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}