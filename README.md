# Secure File Vault

A zero-knowledge, end-to-end encrypted file storage platform built with modern web technologies. This project provides secure file storage with client-side encryption, ensuring that only users have access to their data.

## 🔐 Features

### Core Security Features
- **Zero-Knowledge Architecture**: Server never sees unencrypted data
- **End-to-End Encryption**: All files encrypted client-side using AES-256-GCM
- **Secure Key Management**: PBKDF2-based key derivation with user passwords
- **File Integrity**: Cryptographic verification of file authenticity
- **Secure Sharing**: Encrypted file sharing with time-limited access tokens

### User Features
- **Secure File Upload**: Drag-and-drop file upload with client-side encryption
- **File Management**: Organize, rename, and delete encrypted files
- **Secure Sharing**: Share files with expiration dates and access controls
- **Cross-Platform**: Works on desktop and mobile browsers
- **Offline Capable**: Progressive Web App with offline functionality

### Technical Features
- **Monorepo Architecture**: Organized codebase with shared packages
- **Type Safety**: Full TypeScript implementation
- **Modern Stack**: Next.js frontend, NestJS backend
- **Scalable Storage**: Support for local and cloud storage providers
- **Real-time Updates**: WebSocket-based file synchronization

## 🏗️ Architecture

### Monorepo Structure

```
secure-file-vault/
├── apps/
│   ├── web/              # Next.js frontend application
│   └── api/              # NestJS backend API
├── packages/
│   ├── shared-types/     # Common TypeScript types
│   ├── crypto-core/      # Cryptographic utilities
│   ├── ui-components/    # Reusable UI components
│   ├── api-client/       # API client SDK
│   └── config/           # Shared configurations
├── libs/
│   ├── database/         # Database utilities and Prisma
│   ├── auth/             # Authentication utilities
│   └── testing/          # Testing utilities
├── docs/                 # Documentation
├── scripts/              # Development and deployment scripts
└── tools/                # Build tools and generators
```

### Technology Stack

**Frontend (apps/web)**
- Next.js 14 with App Router
- TypeScript for type safety
- Tailwind CSS for styling
- Web Crypto API for encryption
- Progressive Web App capabilities

**Backend (apps/api)**
- NestJS framework
- TypeScript
- PostgreSQL database with Prisma ORM
- Redis for caching and sessions
- JWT authentication

**Shared Packages**
- `@crypto-core/index`: Client-side cryptography
- `@shared-types/index`: Common TypeScript interfaces
- `@ui-components/index`: Reusable React components
- `@api-client/index`: Type-safe API client

**Development Tools**
- Turbo for monorepo build orchestration
- ESLint + Prettier for code quality
- Husky for git hooks
- Docker for local development
- GitHub Actions for CI/CD

## 🚀 Quick Start

### Prerequisites

- **Node.js 18+**: [Download from nodejs.org](https://nodejs.org/)
- **Docker**: [Download from docker.com](https://docker.com/)
- **Git**: For version control

### Automated Setup

Run the automated setup script for a complete development environment:

```bash
git clone <repository-url>
cd secure-file-vault
./scripts/dev-setup.sh
```

This script will:
- ✅ Check system requirements
- ✅ Install dependencies
- ✅ Set up environment files
- ✅ Start Docker services (PostgreSQL, Redis)
- ✅ Set up the database
- ✅ Run initial build
- ✅ Configure git hooks

### Manual Setup

If you prefer manual setup:

1. **Clone and install dependencies**:
   ```bash
   git clone <repository-url>
   cd secure-file-vault
   npm ci
   ```

2. **Set up environment files**:
   ```bash
   cp .env.example .env
   cp apps/api/.env.example apps/api/.env
   cp apps/web/.env.example apps/web/.env.local
   ```

3. **Start services**:
   ```bash
   npm run services:up
   ```

4. **Set up database** (when Prisma is configured):
   ```bash
   npm run db:migrate
   ```

5. **Start development**:
   ```bash
   npm run dev
   ```

### Verify Installation

Run the verification script to ensure everything is working:

```bash
./scripts/verify-dev-env.sh
```

## 🛠️ Development

### Available Scripts

**Development**
- `npm run dev` - Start all applications in development mode
- `npm run dev:services` - Start Docker services only
- `npm run dev:tools` - Start development tools (pgAdmin, Redis Commander)

**Building**
- `npm run build` - Build all packages and applications
- `npm run clean` - Clean all build artifacts

**Code Quality**
- `npm run lint` - Run ESLint on all packages
- `npm run lint:fix` - Run ESLint with auto-fix
- `npm run format` - Format code with Prettier
- `npm run type-check` - Run TypeScript type checking

**Testing**
- `npm run test` - Run all tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage

**Database**
- `npm run db:migrate` - Run database migrations
- `npm run db:studio` - Open Prisma Studio
- `npm run db:reset` - Reset database

### Development Workflow

1. **Create a feature branch**:
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes** in the appropriate packages

3. **Run quality checks**:
   ```bash
   npm run lint
   npm run type-check
   npm run test
   ```

4. **Commit changes** (pre-commit hooks will run automatically)

5. **Push and create a pull request**

### Working with the Monorepo

- **Adding dependencies**: Use workspace-specific commands
  ```bash
  npm install <package> -w apps/web
  npm install <package> -w packages/crypto-core
  ```

- **Building specific packages**:
  ```bash
  npx turbo run build --filter=apps/web
  ```

- **Running tests for specific packages**:
  ```bash
  npx turbo run test --filter=packages/crypto-core
  ```

## 🌐 Accessing the Application

Once the development environment is running:

- **Web Application**: http://localhost:3000
- **API Server**: http://localhost:3001
- **API Documentation**: http://localhost:3001/api (when Swagger is enabled)

### Development Tools

- **pgAdmin**: http://localhost:8080 (<EMAIL> / admin_password)
- **Redis Commander**: http://localhost:8081
- **Prisma Studio**: `npm run db:studio`

## 🔒 Security Features

### Cryptographic Implementation

The application uses industry-standard cryptographic practices:

- **Encryption**: AES-256-GCM for file encryption
- **Key Derivation**: PBKDF2 with 100,000 iterations
- **Random Generation**: Cryptographically secure random number generation
- **Integrity**: HMAC-based authentication codes

### Zero-Knowledge Architecture

- Files are encrypted client-side before upload
- Server never has access to encryption keys
- User passwords are never transmitted in plain text
- Metadata is minimized and encrypted when possible

### Security Best Practices

- Content Security Policy (CSP) headers
- HTTPS enforcement in production
- Secure session management
- Rate limiting and DDoS protection
- Input validation and sanitization

## 📊 Database Schema

The application uses PostgreSQL with the following main entities:

- **Users**: User accounts and authentication
- **Files**: Encrypted file metadata and storage references
- **Shares**: Secure file sharing with expiration
- **Sessions**: User session management

## 🚀 Deployment

### Environment Configuration

For production deployment:

1. **Set environment variables**:
   - Use strong, unique secrets for JWT_SECRET
   - Configure secure database credentials
   - Set NODE_ENV to "production"
   - Configure HTTPS URLs

2. **Database setup**:
   - Run migrations: `npm run db:migrate`
   - Ensure database backups are configured

3. **Security checklist**:
   - Enable HTTPS
   - Configure proper CORS origins
   - Set up rate limiting
   - Configure monitoring and logging

### Docker Deployment

Build and deploy using Docker:

```bash
# Build production images
docker build -t secure-vault-web ./apps/web
docker build -t secure-vault-api ./apps/api

# Run with docker-compose
docker-compose -f docker-compose.prod.yml up -d
```

## 🧪 Testing

### Test Structure

- **Unit Tests**: Individual component and function testing
- **Integration Tests**: API endpoint and database testing
- **End-to-End Tests**: Full user workflow testing
- **Security Tests**: Cryptographic function verification

### Running Tests

```bash
# Run all tests
npm run test

# Run with coverage
npm run test:coverage

# Run specific test suites
npx turbo run test --filter=packages/crypto-core
```

## 📚 Documentation

- **[Development Guide](docs/DEVELOPMENT.md)**: Detailed development setup and workflow
- **[API Documentation](docs/API.md)**: Backend API reference
- **[Deployment Guide](docs/DEPLOYMENT.md)**: Production deployment instructions
- **[Security Guide](docs/SECURITY.md)**: Security implementation details

## 🤝 Contributing

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Make your changes** following the coding standards
4. **Run tests**: `npm run test`
5. **Commit changes**: `git commit -m 'Add amazing feature'`
6. **Push to branch**: `git push origin feature/amazing-feature`
7. **Open a Pull Request**

### Code Standards

- Follow TypeScript best practices
- Use ESLint and Prettier configurations
- Write comprehensive tests
- Document public APIs
- Follow security best practices

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the [docs/](docs/) directory
- **Issues**: Open an issue on GitHub
- **Discussions**: Use GitHub Discussions for questions

## 🔄 Project Status

This project is actively developed with the following completed features:

- ✅ Development environment setup
- ✅ Monorepo architecture
- ✅ Basic project structure
- ✅ CI/CD pipeline
- 🚧 Cryptographic core implementation (in progress)
- 🚧 User authentication system (in progress)
- 🚧 File upload and encryption (in progress)
- 🚧 Secure file sharing (in progress)

## 🙏 Acknowledgments

- Built with modern web technologies and security best practices
- Inspired by zero-knowledge security principles
- Community-driven development approach

---

**⚠️ Security Notice**: This application handles sensitive user data. Always review security implementations and conduct security audits before production deployment.