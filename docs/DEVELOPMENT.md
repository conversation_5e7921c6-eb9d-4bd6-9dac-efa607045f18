# Development Environment Setup

This document provides comprehensive instructions for setting up and working with the Secure File Vault development environment.

## Quick Start

For a fully automated setup, run:

```bash
./scripts/dev-setup.sh
```

This script will:

- Check system requirements
- Install dependencies
- Set up environment files
- Start Docker services (PostgreSQL, Redis)
- Set up the database
- Run initial build
- Configure git hooks

## Manual Setup

### Prerequisites

- **Node.js 18+**: [Download from nodejs.org](https://nodejs.org/)
- **npm 9+**: Comes with Node.js
- **Docker**: [Download from docker.com](https://docker.com/)
- **Docker Compose**: Usually included with Docker Desktop

### 1. Install Dependencies

```bash
npm ci
```

### 2. Environment Configuration

Copy environment files and customize as needed:

```bash
# Root environment
cp .env.example .env

# API environment
cp apps/api/.env.example apps/api/.env

# Web environment
cp apps/web/.env.example apps/web/.env.local
```

### 3. Start Services

Start PostgreSQL and Redis using Docker:

```bash
npm run services:up
```

Or start individual services:

```bash
npm run dev:services  # PostgreSQL + Redis
npm run dev:tools     # pgAdmin + Redis Commander (optional)
```

### 4. Database Setup

If Prisma is configured:

```bash
npm run db:generate  # Generate Prisma client
npm run db:migrate   # Run migrations
```

### 5. Start Development

```bash
npm run dev  # Start all applications in development mode
```

## Available Scripts

### Development

- `npm run dev` - Start all applications in development mode
- `npm run dev:full` - Start services and applications together
- `npm run dev:services` - Start Docker services only
- `npm run dev:tools` - Start development tools (pgAdmin, Redis Commander)

### Building

- `npm run build` - Build all packages and applications
- `npm run clean` - Clean all build artifacts
- `npm run clean:cache` - Clean Turbo cache only

### Code Quality

- `npm run lint` - Run ESLint on all packages
- `npm run lint:fix` - Run ESLint with auto-fix
- `npm run format` - Format code with Prettier
- `npm run format:check` - Check code formatting
- `npm run type-check` - Run TypeScript type checking

### Testing

- `npm run test` - Run all tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage report

### Database

- `npm run db:migrate` - Run database migrations
- `npm run db:generate` - Generate Prisma client
- `npm run db:studio` - Open Prisma Studio
- `npm run db:reset` - Reset database (destructive)

### Services

- `npm run services:up` - Start all Docker services
- `npm run services:down` - Stop all Docker services
- `npm run services:logs` - View service logs

## Monorepo Structure

```
secure-file-vault/
├── apps/
│   ├── web/          # Next.js frontend application
│   └── api/          # NestJS backend application
├── packages/
│   ├── shared-types/ # Common TypeScript types
│   ├── crypto-core/  # Cryptographic utilities
│   ├── ui-components/# Reusable UI components
│   ├── api-client/   # API client SDK
│   └── config/       # Shared configurations
├── libs/
│   ├── database/     # Database utilities and Prisma
│   ├── auth/         # Authentication utilities
│   └── testing/      # Testing utilities
└── tools/
    ├── scripts/      # Development and build scripts
    └── generators/   # Code generators
```

## Development Workflow

### 1. Feature Development

1. Create a feature branch:

   ```bash
   git checkout -b feature/your-feature-name
   ```

2. Make your changes in the appropriate packages

3. Run quality checks:

   ```bash
   npm run lint
   npm run type-check
   npm run test
   ```

4. Commit your changes (pre-commit hooks will run automatically)

5. Push and create a pull request

### 2. Working with Packages

The monorepo uses Turbo for build orchestration and caching:

- **Shared packages** are automatically built when dependencies change
- **Build caching** speeds up repeated builds
- **Parallel execution** runs tasks across packages simultaneously

### 3. Adding Dependencies

- **Workspace root**: `npm install <package> -w root`
- **Specific app**: `npm install <package> -w apps/web`
- **Specific package**: `npm install <package> -w packages/crypto-core`

### 4. Creating New Packages

1. Create the package directory structure
2. Add `package.json` with appropriate configuration
3. Update workspace references in root `package.json`
4. Add build configuration to `turbo.json`

## Docker Services

### PostgreSQL

- **Host**: localhost:5432
- **Database**: secure_vault_dev
- **Username**: vault_user
- **Password**: vault_password
- **Test Database**: secure_vault_test

### Redis

- **Host**: localhost:6380
- **Password**: redis_password

### Development Tools

When running `npm run dev:tools`:

- **pgAdmin**: http://localhost:8080
  - Email: <EMAIL>
  - Password: admin_password

- **Redis Commander**: http://localhost:8081

## Troubleshooting

### Common Issues

1. **Port conflicts**: Ensure ports 3000, 3001, 5432, 6380, 8080, 8081 are available

2. **Docker issues**:

   ```bash
   docker-compose down
   docker system prune -f
   npm run services:up
   ```

3. **Build cache issues**:

   ```bash
   npm run clean:cache
   npm run build
   ```

4. **Database connection issues**:

   ```bash
   npm run services:down
   npm run services:up
   sleep 10
   npm run db:migrate
   ```

5. **Node modules issues**:
   ```bash
   npm run clean
   npm ci
   ```

### Performance Tips

1. **Use build caching**: Turbo automatically caches builds, but you can clear cache with `npm run clean:cache`

2. **Parallel development**: Run `npm run dev` to start all services in parallel

3. **Selective building**: Use `turbo run build --filter=apps/web` to build specific packages

4. **Watch mode**: Use `npm run test:watch` for continuous testing during development

### Environment Variables

Key environment variables for development:

```bash
# Database
DATABASE_URL="postgresql://vault_user:vault_password@localhost:5432/secure_vault_dev"

# Redis
REDIS_URL="redis://:redis_password@localhost:6380"

# API
PORT=3001
JWT_SECRET="your-development-secret"

# Web
NEXT_PUBLIC_API_URL="http://localhost:3001/api"
```

## Security Considerations

Even in development:

1. **Never commit real secrets** to version control
2. **Use strong passwords** for local services
3. **Keep dependencies updated** regularly
4. **Run security scans** with `npm audit`

## Getting Help

- Check the [main README](../README.md) for project overview
- Review [API documentation](./API.md) for backend details
- Check [deployment guide](./DEPLOYMENT.md) for production setup
- Open an issue for bugs or feature requests
