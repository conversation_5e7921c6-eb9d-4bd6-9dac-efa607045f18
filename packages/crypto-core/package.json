{"name": "@crypto-core/index", "version": "0.1.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src --ext .ts,.tsx", "type-check": "tsc --noEmit", "clean": "rm -rf dist", "test": "jest"}, "dependencies": {"@shared-types/index": "*"}, "devDependencies": {"typescript": "^5.3.0", "@types/jest": "^29.5.0", "jest": "^29.5.0"}}