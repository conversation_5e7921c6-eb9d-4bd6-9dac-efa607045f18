export class BrowserCompat {
  /**
   * Check if the current browser supports all required crypto features
   */
  static checkSupport(): {
    supported: boolean;
    missing: string[];
    warnings: string[];
  } {
    const missing: string[] = [];
    const warnings: string[] = [];

    // Check Web Crypto API
    if (typeof crypto === 'undefined' || typeof crypto.subtle === 'undefined') {
      missing.push('Web Crypto API');
    }

    // Check IndexedDB
    if (typeof indexedDB === 'undefined') {
      missing.push('IndexedDB');
    }

    // Check for secure context (HTTPS)
    if (typeof window !== 'undefined' && !window.isSecureContext) {
      warnings.push('Secure context (HTTPS) required for full functionality');
    }

    // Check specific crypto algorithms
    if (typeof crypto !== 'undefined' && typeof crypto.subtle !== 'undefined') {
      // These checks would need to be async in real implementation
      // For now, we assume support if Web Crypto API is available
    }

    return {
      supported: missing.length === 0,
      missing,
      warnings,
    };
  }

  /**
   * Get browser information for debugging
   */
  static getBrowserInfo(): {
    userAgent: string;
    isSecureContext: boolean;
    cryptoSupported: boolean;
    indexedDBSupported: boolean;
  } {
    return {
      userAgent:
        typeof navigator !== 'undefined' ? navigator.userAgent : 'Unknown',
      isSecureContext:
        typeof window !== 'undefined' ? window.isSecureContext : false,
      cryptoSupported:
        typeof crypto !== 'undefined' && typeof crypto.subtle !== 'undefined',
      indexedDBSupported: typeof indexedDB !== 'undefined',
    };
  }

  /**
   * Provide fallback suggestions for unsupported browsers
   */
  static getFallbackSuggestions(): string[] {
    const suggestions: string[] = [];
    const support = BrowserCompat.checkSupport();

    if (support.missing.includes('Web Crypto API')) {
      suggestions.push(
        'Please use a modern browser that supports Web Crypto API (Chrome 37+, Firefox 34+, Safari 7+)'
      );
    }

    if (support.missing.includes('IndexedDB')) {
      suggestions.push(
        'Please use a browser that supports IndexedDB for secure key storage'
      );
    }

    if (
      support.warnings.includes(
        'Secure context (HTTPS) required for full functionality'
      )
    ) {
      suggestions.push(
        'Please access the application over HTTPS for full security features'
      );
    }

    return suggestions;
  }
}
