import {
  CryptoConfig,
  KeyPair,
  EncryptionResult,
  DecryptionParams,
} from './types';

export class CryptoService {
  private static readonly DEFAULT_CONFIG: CryptoConfig = {
    algorithm: 'AES-GCM',
    keyLength: 256,
    iterations: 100000,
    saltLength: 32,
  };

  /**
   * Generate cryptographically secure random bytes
   */
  static generateSecureRandom(length: number): Uint8Array {
    return crypto.getRandomValues(new Uint8Array(length));
  }

  /**
   * Derive a key from password using PBKDF2
   */
  static async deriveKey(
    password: string,
    salt: Uint8Array,
    iterations: number = CryptoService.DEFAULT_CONFIG.iterations
  ): Promise<CryptoKey> {
    const encoder = new TextEncoder();
    const passwordBuffer = encoder.encode(password);

    const baseKey = await crypto.subtle.importKey(
      'raw',
      passwordBuffer,
      'PBKDF2',
      false,
      ['deriveKey']
    );

    return crypto.subtle.deriveKey(
      {
        name: 'PBKDF2',
        salt: salt as BufferSource,
        iterations: iterations,
        hash: 'SHA-256',
      },
      baseKey,
      {
        name: 'AES-GCM',
        length: CryptoService.DEFAULT_CONFIG.keyLength,
      },
      false,
      ['encrypt', 'decrypt']
    );
  }

  /**
   * Encrypt data using AES-GCM
   */
  static async encryptData(
    data: ArrayBuffer,
    key: CryptoKey
  ): Promise<EncryptionResult> {
    const iv = CryptoService.generateSecureRandom(12); // 96-bit IV for GCM

    const encryptedData = await crypto.subtle.encrypt(
      {
        name: 'AES-GCM',
        iv: iv as BufferSource,
      },
      key,
      data
    );

    return {
      encryptedData,
      iv,
    };
  }

  /**
   * Decrypt data using AES-GCM
   */
  static async decryptData(params: DecryptionParams): Promise<ArrayBuffer> {
    return crypto.subtle.decrypt(
      {
        name: 'AES-GCM',
        iv: params.iv,
      },
      params.key,
      params.encryptedData
    );
  }

  /**
   * Generate RSA key pair for sharing operations
   */
  static async generateKeyPair(): Promise<KeyPair> {
    const keyPair = await crypto.subtle.generateKey(
      {
        name: 'RSA-OAEP',
        modulusLength: 4096,
        publicExponent: new Uint8Array([1, 0, 1]),
        hash: 'SHA-256',
      },
      true,
      ['encrypt', 'decrypt']
    );

    return keyPair;
  }

  /**
   * Encrypt data with RSA public key
   */
  static async encryptWithPublicKey(
    data: ArrayBuffer,
    publicKey: CryptoKey
  ): Promise<ArrayBuffer> {
    return crypto.subtle.encrypt(
      {
        name: 'RSA-OAEP',
      },
      publicKey,
      data
    );
  }

  /**
   * Decrypt data with RSA private key
   */
  static async decryptWithPrivateKey(
    encryptedData: ArrayBuffer,
    privateKey: CryptoKey
  ): Promise<ArrayBuffer> {
    return crypto.subtle.decrypt(
      {
        name: 'RSA-OAEP',
      },
      privateKey,
      encryptedData
    );
  }
}

// Export utility functions
export const {
  generateSecureRandom,
  deriveKey,
  encryptData,
  decryptData,
  generateKeyPair,
  encryptWithPublicKey,
  decryptWithPrivateKey,
} = CryptoService;
