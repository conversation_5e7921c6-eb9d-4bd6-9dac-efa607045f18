import { AuthResponse, User } from '@shared-types/index';

import { ApiClient } from './client';

export class AuthClient {
  constructor(private apiClient: ApiClient) {}

  /**
   * Register a new user
   */
  async register(
    email: string,
    encryptedMasterKey: string,
    keyDerivationParams: any
  ): Promise<AuthResponse> {
    const response = await this.apiClient.post<AuthResponse>('/auth/register', {
      email,
      encryptedMasterKey,
      keyDerivationParams,
    });

    if (response.success && response.data) {
      this.apiClient.setAuthTokens({
        accessToken: response.data.token,
        expiresAt: response.data.expiresAt,
      });
    }

    return response.data!;
  }

  /**
   * Login user
   */
  async login(email: string, challengeResponse: string): Promise<AuthResponse> {
    const response = await this.apiClient.post<AuthResponse>('/auth/login', {
      email,
      challengeResponse,
    });

    if (response.success && response.data) {
      this.apiClient.setAuthTokens({
        accessToken: response.data.token,
        expiresAt: response.data.expiresAt,
      });
    }

    return response.data!;
  }

  /**
   * Logout user
   */
  async logout(): Promise<void> {
    await this.apiClient.post('/auth/logout');
    this.apiClient.clearAuthTokens();
  }

  /**
   * Get current user profile
   */
  async getProfile(): Promise<User> {
    const response = await this.apiClient.get<User>('/auth/profile');
    return response.data!;
  }

  /**
   * Refresh authentication token
   */
  async refreshToken(): Promise<AuthResponse> {
    const response = await this.apiClient.post<AuthResponse>('/auth/refresh');

    if (response.success && response.data) {
      this.apiClient.setAuthTokens({
        accessToken: response.data.token,
        expiresAt: response.data.expiresAt,
      });
    }

    return response.data!;
  }
}
