import { FileRecord } from '@shared-types/index';

import { ApiClient } from './client';

export class FilesClient {
  constructor(private apiClient: ApiClient) {}

  /**
   * Upload a file
   */
  async uploadFile(
    encryptedFile: Blob,
    encryptedMetadata: any
  ): Promise<FileRecord> {
    const formData = new FormData();
    formData.append('file', encryptedFile);
    formData.append('metadata', JSON.stringify(encryptedMetadata));

    const response = await this.apiClient.request<FileRecord>('/files/upload', {
      method: 'POST',
      body: formData,
      headers: {}, // Let browser set Content-Type for FormData
    });

    return response.data!;
  }

  /**
   * Download a file
   */
  async downloadFile(fileId: string): Promise<Blob> {
    const response = await fetch(
      `${this.apiClient['baseUrl']}/files/${fileId}`,
      {
        headers: {
          Authorization: `Bearer ${this.apiClient['authTokens']?.accessToken}`,
        },
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to download file: ${response.statusText}`);
    }

    return response.blob();
  }

  /**
   * List user files
   */
  async listFiles(folderId?: string): Promise<FileRecord[]> {
    const endpoint = folderId ? `/files?folderId=${folderId}` : '/files';
    const response = await this.apiClient.get<FileRecord[]>(endpoint);
    return response.data!;
  }

  /**
   * Delete a file
   */
  async deleteFile(fileId: string): Promise<void> {
    await this.apiClient.delete(`/files/${fileId}`);
  }

  /**
   * Get file metadata
   */
  async getFileMetadata(fileId: string): Promise<FileRecord> {
    const response = await this.apiClient.get<FileRecord>(
      `/files/${fileId}/metadata`
    );
    return response.data!;
  }
}
