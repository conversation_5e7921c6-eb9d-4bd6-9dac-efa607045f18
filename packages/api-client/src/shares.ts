import { ShareRecord } from '@shared-types/index';

import { ApiClient } from './client';

export class SharesClient {
  constructor(private apiClient: ApiClient) {}

  /**
   * Create a share link
   */
  async createShare(fileId: string, permissions: any): Promise<ShareRecord> {
    const response = await this.apiClient.post<ShareRecord>('/shares', {
      fileId,
      permissions,
    });
    return response.data!;
  }

  /**
   * Access a shared file
   */
  async accessSharedFile(
    shareToken: string,
    accessKey?: string
  ): Promise<Blob> {
    const endpoint = accessKey
      ? `/shares/${shareToken}?key=${accessKey}`
      : `/shares/${shareToken}`;

    const response = await fetch(`${this.apiClient['baseUrl']}${endpoint}`);

    if (!response.ok) {
      throw new Error(`Failed to access shared file: ${response.statusText}`);
    }

    return response.blob();
  }

  /**
   * Get share information
   */
  async getShareInfo(shareToken: string): Promise<ShareRecord> {
    const response = await this.apiClient.get<ShareRecord>(
      `/shares/${shareToken}/info`
    );
    return response.data!;
  }

  /**
   * Revoke a share
   */
  async revokeShare(shareId: string): Promise<void> {
    await this.apiClient.delete(`/shares/${shareId}`);
  }

  /**
   * List user's shares
   */
  async listShares(): Promise<ShareRecord[]> {
    const response = await this.apiClient.get<ShareRecord[]>('/shares');
    return response.data!;
  }
}
