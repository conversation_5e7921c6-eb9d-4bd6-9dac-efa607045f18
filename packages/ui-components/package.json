{"name": "@ui-components/index", "version": "0.1.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src --ext .ts,.tsx", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"react": "^18", "react-dom": "^18", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-slot": "^1.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0"}, "devDependencies": {"typescript": "^5.3.0", "@types/react": "^18", "@types/react-dom": "^18"}}