// User types
export interface User {
  id: string;
  email: string;
  encryptedMasterKey: string;
  keyDerivationParams: {
    salt: string;
    iterations: number;
    algorithm: string;
  };
  publicKey: string;
  storageQuota: number;
  storageUsed: number;
  tier: 'free' | 'professional' | 'business';
  createdAt: Date;
  lastLoginAt: Date;
  twoFactorEnabled: boolean;
}

// File types
export interface FileRecord {
  id: string;
  userId: string;
  encryptedName: string;
  encryptedSize: string;
  mimeType: string;
  encryptedFileKey: string;
  storageLocation: string;
  checksum: string;
  createdAt: Date;
  modifiedAt: Date;
  folderId?: string;
  tags?: string[];
}

// Share types
export interface ShareRecord {
  id: string;
  fileId: string;
  ownerId: string;
  shareToken: string;
  encryptedFileKey: string;
  permissions: {
    canDownload: boolean;
    canView: boolean;
    expiresAt?: Date;
    maxAccesses?: number;
    passwordProtected: boolean;
  };
  accessLog: AccessLogEntry[];
  createdAt: Date;
}

export interface AccessLogEntry {
  id: string;
  timestamp: Date;
  ipAddress?: string;
  userAgent?: string;
  action: 'view' | 'download';
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface AuthResponse {
  token: string;
  user: User;
  expiresAt: Date;
}

// Common enums
export enum UserTier {
  FREE = 'free',
  PROFESSIONAL = 'professional',
  BUSINESS = 'business',
}

export enum FilePermission {
  READ = 'read',
  WRITE = 'write',
  DELETE = 'delete',
  SHARE = 'share',
}

export enum SharePermission {
  VIEW = 'view',
  DOWNLOAD = 'download',
}
