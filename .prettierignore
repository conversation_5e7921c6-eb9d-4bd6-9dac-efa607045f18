# Dependencies
node_modules/
**/node_modules/

# Build outputs
dist/
build/
.next/
.turbo/
**/dist/
**/build/

# Test coverage
coverage/
**/coverage/

# Minified files
*.min.js
*.min.css

# Lock files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Generated files
*.d.ts
prisma/migrations/
**/prisma/migrations/

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db