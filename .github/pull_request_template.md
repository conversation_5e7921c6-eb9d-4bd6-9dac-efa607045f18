# Pull Request

## Description

Brief description of the changes in this PR.

## Type of Change

- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update
- [ ] Performance improvement
- [ ] Code refactoring
- [ ] Security improvement

## Related Issues

Fixes #(issue number)

## Changes Made

- Change 1
- Change 2
- Change 3

## Testing

- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] E2E tests pass (if applicable)
- [ ] Manual testing completed

### Test Coverage

- [ ] New code is covered by tests
- [ ] Existing tests updated as needed
- [ ] Coverage percentage maintained or improved

## Security Checklist

- [ ] No sensitive data exposed in logs or responses
- [ ] Input validation implemented where needed
- [ ] Authentication/authorization checks in place
- [ ] Cryptographic operations reviewed
- [ ] No hardcoded secrets or credentials

## Code Quality

- [ ] Code follows project style guidelines
- [ ] Self-review completed
- [ ] Code is documented (comments, JSDoc, etc.)
- [ ] No console.log statements left in production code
- [ ] Error handling implemented appropriately

## Database Changes

- [ ] Database migrations included (if applicable)
- [ ] Migration is reversible
- [ ] Database changes tested locally
- [ ] No breaking changes to existing data

## Deployment

- [ ] Environment variables updated (if needed)
- [ ] Configuration changes documented
- [ ] Deployment instructions provided (if needed)
- [ ] Backward compatibility maintained

## Screenshots (if applicable)

Add screenshots to help explain your changes.

## Additional Notes

Any additional information that reviewers should know.
