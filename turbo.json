{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local", ".env.example", "docker-compose.yml", "tsconfig.json", ".eslintrc.js", ".prettier<PERSON>"], "globalEnv": ["NODE_ENV", "DATABASE_URL", "REDIS_URL", "JWT_SECRET"], "pipeline": {"build": {"dependsOn": ["^build"], "inputs": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.js", "src/**/*.jsx", "package.json", "tsconfig.json", "next.config.*", "nest-cli.json", "prisma/schema.prisma"], "outputs": [".next/**", "!.next/cache/**", "dist/**", "build/**", "lib/**", "*.tsbuildinfo"], "outputMode": "hash-only"}, "dev": {"cache": false, "persistent": true, "dependsOn": ["^build"]}, "lint": {"dependsOn": ["^build"], "inputs": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.js", "src/**/*.jsx", ".eslintrc.js", ".es<PERSON><PERSON><PERSON>", "package.json"], "outputs": []}, "lint:fix": {"dependsOn": ["^build"], "inputs": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.js", "src/**/*.jsx", ".eslintrc.js", ".es<PERSON><PERSON><PERSON>", "package.json"], "outputs": [], "cache": false}, "test": {"dependsOn": ["^build"], "inputs": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.js", "src/**/*.jsx", "**/*.test.*", "**/*.spec.*", "jest.config.*", "vitest.config.*", "package.json"], "outputs": ["coverage/**", "test-results/**"], "env": ["NODE_ENV", "DATABASE_TEST_URL", "REDIS_URL"]}, "test:watch": {"cache": false, "persistent": true}, "test:coverage": {"dependsOn": ["^build"], "inputs": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.js", "src/**/*.jsx", "**/*.test.*", "**/*.spec.*", "jest.config.*", "vitest.config.*", "package.json"], "outputs": ["coverage/**"], "env": ["NODE_ENV", "DATABASE_TEST_URL", "REDIS_URL"]}, "type-check": {"dependsOn": ["^build"], "inputs": ["src/**/*.ts", "src/**/*.tsx", "tsconfig.json", "package.json"], "outputs": ["*.tsbuildinfo"]}, "clean": {"cache": false, "outputs": []}, "db:migrate": {"cache": false, "inputs": ["prisma/schema.prisma", "prisma/migrations/**"], "env": ["DATABASE_URL"]}, "db:generate": {"dependsOn": ["^db:generate"], "inputs": ["prisma/schema.prisma"], "outputs": ["node_modules/.prisma/**", "prisma/generated/**"]}, "db:studio": {"cache": false, "persistent": true, "env": ["DATABASE_URL"]}, "db:reset": {"cache": false, "env": ["DATABASE_URL"]}}, "remoteCache": {"signature": true}}