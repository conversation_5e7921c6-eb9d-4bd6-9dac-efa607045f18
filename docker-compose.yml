services:
  postgres:
    image: postgres:15-alpine
    container_name: secure-vault-postgres
    environment:
      POSTGRES_DB: secure_vault_dev
      POSTGRES_USER: vault_user
      POSTGRES_PASSWORD: vault_password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - '5432:5432'
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U vault_user -d secure_vault_dev']
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: secure-vault-redis
    ports:
      - '6380:6379'
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --requirepass redis_password
    healthcheck:
      test: ['CMD', 'redis-cli', '--raw', 'incr', 'ping']
      interval: 10s
      timeout: 3s
      retries: 5

  # Optional: Redis Commander for Redis GUI
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: secure-vault-redis-commander
    environment:
      REDIS_HOSTS: local:redis:6379:0:redis_password
    ports:
      - '8081:8081'
    depends_on:
      - redis
    profiles:
      - tools

  # Optional: pgAdmin for PostgreSQL GUI
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: secure-vault-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin_password
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - '8080:80'
    depends_on:
      - postgres
    profiles:
      - tools

volumes:
  postgres_data:
  redis_data:

networks:
  default:
    name: secure-vault-network
