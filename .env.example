# ==============================================
# SECURE FILE VAULT - ENVIRONMENT VARIABLES
# ==============================================
# Copy this file to .env and update the values for your environment

# =============================================================================
# GLOBAL CONFIGURATION
# =============================================================================

# Environment
NODE_ENV="development"

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL Database URLs
DATABASE_URL="postgresql://vault_user:vault_password@localhost:5432/secure_vault_dev?schema=public"
DATABASE_TEST_URL="postgresql://vault_user:vault_password@localhost:5432/secure_vault_test?schema=public"

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================

# Redis Configuration
REDIS_URL="redis://:redis_password@localhost:6380"
REDIS_PASSWORD="redis_password"

# =============================================================================
# API CONFIGURATION (for apps/api)
# =============================================================================

# API Server Port
PORT=3001

# JWT Configuration
JWT_SECRET="your-super-secure-jwt-secret-key-change-in-production"
JWT_EXPIRES_IN="7d"
JWT_REFRESH_EXPIRES_IN="30d"

# Frontend URL for CORS
FRONTEND_URL="http://localhost:3000"

# =============================================================================
# WEB APP CONFIGURATION (for apps/web)
# =============================================================================

# Next.js Configuration
NEXT_PUBLIC_API_URL="http://localhost:3001/api"
NEXT_PUBLIC_WEB_URL="http://localhost:3000"

# =============================================================================
# FILE STORAGE CONFIGURATION
# =============================================================================

# Storage Provider Options: "local", "aws", "gcp", "azure"
STORAGE_PROVIDER="local"
STORAGE_BUCKET="secure-vault-files"
STORAGE_PATH="./uploads"
MAX_FILE_SIZE="**********" # 5GB in bytes

# AWS Configuration (if using AWS storage)
AWS_ACCESS_KEY_ID=""
AWS_SECRET_ACCESS_KEY=""
AWS_REGION="us-east-1"
AWS_S3_BUCKET=""

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Password Hashing
BCRYPT_ROUNDS=12
MASTER_KEY_ITERATIONS=100000

# Session Management
SESSION_SECRET="your-session-secret-change-in-production"

# CORS Configuration
CORS_ORIGIN="http://localhost:3000"

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000 # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================

# SMTP Configuration (for notifications)
SMTP_HOST=""
SMTP_PORT=587
SMTP_USER=""
SMTP_PASS=""
FROM_EMAIL="<EMAIL>"

# =============================================================================
# DEVELOPMENT TOOLS
# =============================================================================

# Prisma Studio Port
PRISMA_STUDIO_PORT=5555

# Development Features
ENABLE_SWAGGER=true
ENABLE_PLAYGROUND=true

# Logging
LOG_LEVEL="debug"
ENABLE_REQUEST_LOGGING=true

# =============================================================================
# PRODUCTION CONFIGURATION NOTES
# =============================================================================

# For production deployment, ensure you:
# 1. Use strong, unique secrets for JWT_SECRET and SESSION_SECRET
# 2. Use secure database credentials with limited permissions
# 3. Set NODE_ENV to "production"
# 4. Configure proper CORS origins
# 5. Use HTTPS URLs for production domains
# 6. Set appropriate rate limits
# 7. Configure proper email settings
# 8. Disable development tools (ENABLE_SWAGGER, ENABLE_PLAYGROUND)