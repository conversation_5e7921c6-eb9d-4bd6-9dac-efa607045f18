#!/bin/bash

# Secure File Vault - Development Environment Setup Script
# This script sets up the complete development environment

set -e

echo "🚀 Setting up Secure File Vault development environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_requirements() {
    print_status "Checking system requirements..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 18+ from https://nodejs.org/"
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "Node.js version 18+ is required. Current version: $(node -v)"
        exit 1
    fi
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install npm."
        exit 1
    fi
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker from https://docker.com/"
        exit 1
    fi
    
    # Check Docker Compose
    if ! docker compose version &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose."
        exit 1
    fi
    
    print_success "All requirements are met!"
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    npm ci
    print_success "Dependencies installed!"
}

# Setup environment files
setup_environment() {
    print_status "Setting up environment files..."
    
    # Root .env
    if [ ! -f .env ]; then
        cp .env.example .env
        print_success "Created root .env file"
    else
        print_warning "Root .env file already exists, skipping..."
    fi
    
    # API .env
    if [ ! -f apps/api/.env ]; then
        cp apps/api/.env.example apps/api/.env
        print_success "Created API .env file"
    else
        print_warning "API .env file already exists, skipping..."
    fi
    
    # Web .env
    if [ ! -f apps/web/.env.local ]; then
        cp apps/web/.env.example apps/web/.env.local
        print_success "Created Web .env.local file"
    else
        print_warning "Web .env.local file already exists, skipping..."
    fi
}

# Start Docker services
start_services() {
    print_status "Starting Docker services (PostgreSQL, Redis)..."
    
    # Stop any existing containers
    docker compose down 2>/dev/null || true
    
    # Start services
    docker compose up -d postgres redis
    
    # Wait for services to be ready
    print_status "Waiting for services to be ready..."
    sleep 10
    
    # Check if PostgreSQL is ready
    if docker compose exec -T postgres pg_isready -U vault_user -d secure_vault_dev; then
        print_success "PostgreSQL is ready!"
    else
        print_error "PostgreSQL failed to start properly"
        exit 1
    fi
    
    # Check if Redis is ready
    if docker compose exec -T redis redis-cli --no-auth-warning -a redis_password ping | grep -q PONG; then
        print_success "Redis is ready!"
    else
        print_error "Redis failed to start properly"
        exit 1
    fi
}

# Setup database
setup_database() {
    print_status "Setting up database..."
    
    # Generate Prisma client (if Prisma is configured)
    if [ -f "apps/api/prisma/schema.prisma" ]; then
        print_status "Generating Prisma client..."
        npm run db:generate 2>/dev/null || print_warning "Prisma not configured yet, skipping..."
        
        print_status "Running database migrations..."
        npm run db:migrate 2>/dev/null || print_warning "No migrations to run yet, skipping..."
    else
        print_warning "Prisma schema not found, skipping database setup..."
    fi
}

# Run initial build
initial_build() {
    print_status "Running initial build..."
    npm run build
    print_success "Initial build completed!"
}

# Setup git hooks
setup_git_hooks() {
    print_status "Setting up git hooks..."
    
    if [ -d ".git" ]; then
        # Initialize husky
        npx husky install 2>/dev/null || print_warning "Husky setup failed, continuing..."
        
        # Make pre-commit hook executable
        chmod +x .husky/pre-commit 2>/dev/null || print_warning "Pre-commit hook not found, continuing..."
        
        print_success "Git hooks configured!"
    else
        print_warning "Not a git repository, skipping git hooks setup..."
    fi
}

# Print final instructions
print_final_instructions() {
    echo ""
    echo "🎉 Development environment setup complete!"
    echo ""
    echo "Next steps:"
    echo "1. Start the development servers:"
    echo "   ${GREEN}npm run dev${NC}"
    echo ""
    echo "2. Or start services and dev servers separately:"
    echo "   ${GREEN}npm run dev:services${NC}  # Start Docker services"
    echo "   ${GREEN}npm run dev${NC}           # Start development servers"
    echo ""
    echo "3. Access the applications:"
    echo "   • Web App: ${BLUE}http://localhost:3000${NC}"
    echo "   • API: ${BLUE}http://localhost:3001${NC}"
    echo "   • PostgreSQL: ${BLUE}localhost:5432${NC} (vault_user/vault_password)"
    echo "   • Redis: ${BLUE}localhost:6379${NC}"
    echo ""
    echo "4. Optional development tools:"
    echo "   ${GREEN}npm run dev:tools${NC}     # Start pgAdmin and Redis Commander"
    echo "   • pgAdmin: ${BLUE}http://localhost:8080${NC} (<EMAIL>/admin_password)"
    echo "   • Redis Commander: ${BLUE}http://localhost:8081${NC}"
    echo ""
    echo "5. Other useful commands:"
    echo "   ${GREEN}npm run lint${NC}          # Run linting"
    echo "   ${GREEN}npm run test${NC}          # Run tests"
    echo "   ${GREEN}npm run format${NC}        # Format code"
    echo "   ${GREEN}npm run type-check${NC}    # Type checking"
    echo "   ${GREEN}npm run db:studio${NC}     # Open Prisma Studio"
    echo ""
    echo "Happy coding! 🚀"
}

# Main execution
main() {
    echo "========================================"
    echo "  Secure File Vault Development Setup  "
    echo "========================================"
    echo ""
    
    check_requirements
    install_dependencies
    setup_environment
    start_services
    setup_database
    initial_build
    setup_git_hooks
    print_final_instructions
}

# Run main function
main "$@"