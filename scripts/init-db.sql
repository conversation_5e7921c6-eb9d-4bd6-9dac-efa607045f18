-- Initialize database for Secure File Vault development
-- This script runs when the PostgreSQL container starts for the first time

-- Create additional databases for testing
CREATE DATABASE secure_vault_test;

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE secure_vault_dev TO vault_user;
GRANT ALL PRIVILEGES ON DATABASE secure_vault_test TO vault_user;

-- Create extensions that might be needed
\c secure_vault_dev;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

\c secure_vault_test;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Log initialization completion
\echo 'Database initialization completed successfully';