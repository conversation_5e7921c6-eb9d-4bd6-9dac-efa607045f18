#!/bin/bash

# Secure File Vault - Development Environment Verification Script
# This script verifies that the development environment is properly set up

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Test counter
TESTS_PASSED=0
TESTS_FAILED=0

# Function to run a test
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    print_status "Testing: $test_name"
    
    if eval "$test_command" > /dev/null 2>&1; then
        print_success "✓ $test_name"
        ((TESTS_PASSED++))
    else
        print_error "✗ $test_name"
        ((TESTS_FAILED++))
    fi
}

echo "========================================"
echo "  Development Environment Verification  "
echo "========================================"
echo ""

# Test 1: Node.js and npm
run_test "Node.js version (>=18)" "node -v | grep -E 'v(1[8-9]|[2-9][0-9])'"
run_test "npm version (>=9)" "npm -v | grep -E '^[9-9]|^[1-9][0-9]' || npm -v | grep -E '^10'"

# Test 2: Docker
run_test "Docker is running" "docker info"
run_test "Docker Compose is available" "docker compose version"

# Test 3: Dependencies
run_test "Node modules installed" "test -d node_modules"
run_test "Turbo is available" "npx turbo --version"

# Test 4: Environment files
run_test "Root .env exists" "test -f .env"
run_test "API .env exists" "test -f apps/api/.env"
run_test "Web .env.local exists" "test -f apps/web/.env.local"

# Test 5: Docker services
print_status "Starting Docker services for testing..."
npm run services:up > /dev/null 2>&1
sleep 5

run_test "PostgreSQL is running" "docker compose exec -T postgres pg_isready -U vault_user -d secure_vault_dev"
run_test "Redis is running" "docker compose exec -T redis redis-cli --no-auth-warning -a redis_password ping | grep -q PONG"

# Test 6: Code quality tools
run_test "ESLint configuration" "test -f .eslintrc.js"
run_test "Prettier configuration" "npx prettier --check package.json"

# Test 7: Build system
print_status "Testing build system (this may take a moment)..."
run_test "TypeScript compilation" "npm run type-check"
run_test "Code formatting check" "npm run format:check"
run_test "Build process" "npm run build"

# Test 8: Git hooks
run_test "Husky is configured" "test -f .husky/pre-commit"
run_test "Pre-commit hook is executable" "test -x .husky/pre-commit"

# Test 9: Development scripts
run_test "Development scripts are available" "grep -q 'dev:' package.json"
run_test "Service management scripts are available" "grep -q 'services:' package.json"

echo ""
echo "========================================"
echo "           Verification Results          "
echo "========================================"
echo ""

if [ $TESTS_FAILED -eq 0 ]; then
    print_success "All tests passed! ($TESTS_PASSED/$((TESTS_PASSED + TESTS_FAILED)))"
    echo ""
    echo "🎉 Your development environment is ready!"
    echo ""
    echo "Next steps:"
    echo "1. Start development: ${GREEN}npm run dev${NC}"
    echo "2. View services: ${GREEN}npm run services:logs${NC}"
    echo "3. Access applications:"
    echo "   • Web: ${BLUE}http://localhost:3000${NC}"
    echo "   • API: ${BLUE}http://localhost:3001${NC}"
    echo ""
else
    print_error "Some tests failed! ($TESTS_PASSED passed, $TESTS_FAILED failed)"
    echo ""
    echo "Please review the failed tests above and:"
    echo "1. Check the development setup guide: ${BLUE}docs/DEVELOPMENT.md${NC}"
    echo "2. Run the setup script: ${BLUE}./scripts/dev-setup.sh${NC}"
    echo "3. Check for port conflicts or missing dependencies"
    echo ""
    exit 1
fi